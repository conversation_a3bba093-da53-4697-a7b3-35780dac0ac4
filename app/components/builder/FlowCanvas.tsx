import React, { useRef, useState } from "react";
import { useAppSelector, useAppDispatch } from "../../hooks/redux-hooks";
import {
  addNode,
  updateNode,
  setSelectedNode,
} from "../../redux/builder/builderSlice";
import { FlowNode } from "../../types";
import NodeComponent from "./NodeComponent";

const FlowCanvas: React.FC = () => {
  const dispatch = useAppDispatch();
  const { currentFlow, selectedNode } = useAppSelector(
    (state) => state.builder
  );
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      dispatch(setSelectedNode(null));
    }
  };

  const handleNodeClick = (node: FlowNode) => {
    dispatch(setSelectedNode(node));
  };

  const handleNodeDragStart = (e: React.MouseEvent, node: FlowNode) => {
    setIsDragging(true);
    const rect = canvasRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left - node.position.x,
        y: e.clientY - rect.top - node.position.y,
      });
    }
  };

  const handleNodeDrag = (e: React.MouseEvent, node: FlowNode) => {
    if (!isDragging) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (rect) {
      const newPosition = {
        x: e.clientX - rect.left - dragOffset.x,
        y: e.clientY - rect.top - dragOffset.y,
      };

      dispatch(
        updateNode({
          ...node,
          position: newPosition,
        })
      );
    }
  };

  const handleNodeDragEnd = () => {
    setIsDragging(false);
  };

  const addNewNode = (
    type: FlowNode["type"],
    position: { x: number; y: number }
  ) => {
    const newNode: FlowNode = {
      id: `${type}-${Date.now()}`,
      type,
      position,
      data: {
        label: type.charAt(0).toUpperCase() + type.slice(1),
      },
    };
    dispatch(addNode(newNode));
  };

  // Create a default flow if none exists
  const displayFlow = currentFlow || {
    id: "default",
    name: "Default Flow",
    nodes: [
      {
        id: "start",
        type: "message" as const,
        position: { x: 200, y: 150 },
        data: { label: "Start" },
      },
      {
        id: "message1",
        type: "message" as const,
        position: { x: 450, y: 250 },
        data: { label: "Message" },
      },
    ],
    connections: [
      {
        id: "start-message1",
        source: "start",
        target: "message1",
      },
    ],
  };

  return (
    <div className="flex-1 relative bg-[#fafbfc] overflow-hidden p-4">
      {/* Canvas Container */}
      <div className="w-full h-full bg-white rounded-xl shadow-sm border border-[#e2e8f0] relative overflow-hidden">
        {/* Canvas */}
        <div
          ref={canvasRef}
          className="w-full h-full relative cursor-default"
          onClick={handleCanvasClick}
          onMouseMove={(e) => {
            if (selectedNode && isDragging) {
              handleNodeDrag(e, selectedNode);
            }
          }}
          onMouseUp={handleNodeDragEnd}
        >
          {/* Grid Pattern */}
          <div
            className="absolute inset-0 opacity-15"
            style={{
              backgroundImage: `
                linear-gradient(rgba(148,163,184,0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(148,163,184,0.2) 1px, transparent 1px)
              `,
              backgroundSize: "24px 24px",
            }}
          />

          {/* Nodes */}
          {displayFlow.nodes.map((node) => (
            <NodeComponent
              key={node.id}
              node={node}
              isSelected={selectedNode?.id === node.id}
              onClick={() => handleNodeClick(node)}
              onDragStart={(e) => handleNodeDragStart(e, node)}
            />
          ))}

          {/* Connections */}
          <svg className="absolute inset-0 pointer-events-none">
            {displayFlow.connections.map((connection) => {
              const sourceNode = displayFlow.nodes.find(
                (n) => n.id === connection.source
              );
              const targetNode = displayFlow.nodes.find(
                (n) => n.id === connection.target
              );

              if (!sourceNode || !targetNode) return null;

              const startX = sourceNode.position.x + 80; // Node width / 2
              const startY = sourceNode.position.y + 25; // Node height / 2
              const endX = targetNode.position.x + 80;
              const endY = targetNode.position.y + 25;

              // Create curved path
              const midX = (startX + endX) / 2;
              const midY = (startY + endY) / 2;
              const controlX = midX;
              const controlY = startY;

              return (
                <path
                  key={connection.id}
                  d={`M ${startX} ${startY} Q ${controlX} ${controlY} ${endX} ${endY}`}
                  stroke="#94a3b8"
                  strokeWidth="2"
                  fill="none"
                  markerEnd="url(#arrowhead)"
                />
              );
            })}

            {/* Arrow marker definition */}
            <defs>
              <marker
                id="arrowhead"
                markerWidth="8"
                markerHeight="6"
                refX="7"
                refY="3"
                orient="auto"
              >
                <polygon points="0 0, 8 3, 0 6" fill="#94a3b8" />
              </marker>
            </defs>
          </svg>
        </div>

        {/* Floating Action Button */}
        <div className="absolute bottom-6 right-6">
          <button
            onClick={() => addNewNode("message", { x: 300, y: 200 })}
            className="w-12 h-12 bg-[#3b82f6] text-white rounded-full shadow-lg hover:bg-[#2563eb] transition-colors flex items-center justify-center"
            title="Add Node"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlowCanvas;
