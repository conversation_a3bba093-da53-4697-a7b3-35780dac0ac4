import React from "react";

const NodesSidebar: React.FC = () => {
  const nodeCategories = [
    {
      title: "Usage",
      color: "bg-[#3b82f6]",
      nodes: [
        {
          id: "message",
          label: "Message",
          icon: (
            <svg
              className="w-4 h-4 text-[#3b82f6]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
            </svg>
          ),
        },
        {
          id: "interactive",
          label: "Interactive Message",
          icon: (
            <svg
              className="w-4 h-4 text-[#10b981]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
          ),
        },
        {
          id: "feedback",
          label: "Feedback",
          icon: (
            <svg
              className="w-4 h-4 text-[#f59e0b]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
          ),
        },
      ],
    },
    {
      title: "Utilities",
      color: "bg-[#6366f1]",
      nodes: [
        {
          id: "utilities",
          label: "Utilities",
          icon: (
            <svg
              className="w-4 h-4 text-[#6366f1]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          ),
        },
      ],
    },
    {
      title: "Marketplace",
      color: "bg-[#8b5cf6]",
      nodes: [
        {
          id: "notification",
          label: "Notification",
          icon: (
            <svg
              className="w-4 h-4 text-[#8b5cf6]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" />
            </svg>
          ),
        },
      ],
    },
  ];

  return (
    <div className="w-[280px] p-4 flex flex-col">
      {/* Floating Container */}
      <div className="bg-white rounded-xl shadow-lg border border-[#e2e8f0] flex flex-col h-full">
        {/* Header */}
        <div className="px-4 py-3 border-b border-[#f1f5f9]">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-semibold text-[#1e293b]">Nodes</h3>
            <button className="p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Node Categories as Rounded Tabs */}
        <div className="flex-1 overflow-y-auto p-3 space-y-3">
          {nodeCategories.map((category) => (
            <div
              key={category.title}
              className="bg-[#f8fafc] rounded-lg border border-[#e2e8f0] p-3"
            >
              {/* Tab Header */}
              <div className="flex items-center space-x-2 mb-3">
                <div className={`w-3 h-3 ${category.color} rounded-full`}></div>
                <h4 className="text-xs font-semibold text-[#1e293b] uppercase tracking-wide">
                  {category.title}
                </h4>
              </div>

              {/* Node Grid */}
              <div className="grid grid-cols-2 gap-2">
                {category.nodes.map((node) => (
                  <div
                    key={node.id}
                    className="bg-white border border-[#e2e8f0] rounded-lg p-3 cursor-pointer hover:shadow-md hover:border-[#3b82f6] transition-all duration-200 group"
                    draggable
                    onDragStart={(e) => {
                      e.dataTransfer.setData("application/reactflow", node.id);
                      e.dataTransfer.effectAllowed = "move";
                    }}
                  >
                    <div className="flex flex-col items-center space-y-2">
                      <div className="w-8 h-8 bg-[#f8fafc] rounded-lg flex items-center justify-center group-hover:bg-[#eff6ff] transition-colors">
                        {node.icon}
                      </div>
                      <span className="text-xs font-medium text-[#1e293b] text-center leading-tight">
                        {node.label}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NodesSidebar;
