// import { defineConfig } from "vite";
// import federation from "@originjs/vite-plugin-federation";
// import { dependencies } from "./package.json";
// import react from "@vitejs/plugin-react";

// // List of shared dependencies with the host application
// const sharedDeps = {
//   react: {
//     requiredVersion: dependencies.react,
//     singleton: true,
//     eager: true,
//   },
//   "react-dom": {
//     requiredVersion: dependencies["react-dom"],
//     singleton: true,
//     eager: true,
//   },
//   "react-router": {
//     requiredVersion: dependencies["react-router"],
//     singleton: true,
//     eager: true,
//   },
//   "@reduxjs/toolkit": {
//     requiredVersion: dependencies["@reduxjs/toolkit"],
//     singleton: true,
//   },
//   "react-redux": {
//     requiredVersion: dependencies["react-redux"],
//     singleton: true,
//   },
// };

// export default defineConfig({
//   plugins: [
//     react(),
//     federation({
//       name: "bot_ui",
//       filename: "remoteEntry.js",
//       // Ensure the file is generated in development mode
//       // library: { type: "module" },
//       exposes: {
//         // Main federation entry point that exports everything
//         "./": "./app/bootstrap.tsx",

//         // Individual components and modules for direct access
//         "./App": "./app/root.tsx",
//         "./routes": "./app/router/routes.ts",
//         "./store": "./app/redux/store.ts",
//         "./components/RouterExample": "./app/components/RouterExample.tsx",
//         "./components/MicroFrontendDemo":
//           "./app/components/MicroFrontendDemo.tsx",
//         "./components/SimpleTestComponent":
//           "./app/components/SimpleTestComponent.tsx",
//         "./routes/Home": "./app/routes/Home/index.tsx",
//         "./routes/Agents": "./app/routes/Agents/index.tsx",
//         "./routes/NewAgent": "./app/routes/Agents/new.tsx",
//       },
//       shared: sharedDeps,
//     }),
//   ],
//   build: {
//     modulePreload: false,
//     target: "esnext",
//     minify: false,
//     cssCodeSplit: false,
//     outDir: "dist/federation",
//     assetsDir: "",
//     emptyOutDir: true,
//     rollupOptions: {
//       input: "./index.html",
//       output: {
//         format: "esm",
//         entryFileNames: "[name].js",
//         chunkFileNames: "[name].js",
//         assetFileNames: "[name].[ext]",
//       },
//     },
//   },
//   server: {
//     port: 5173,
//     strictPort: true,
//     cors: true,
//     fs: {
//       // Allow serving files from the project root
//       allow: ["."],
//     },
//     origin: "http://localhost:5173",
//     headers: {
//       "Access-Control-Allow-Origin": "*",
//       "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
//       "Access-Control-Allow-Headers":
//         "X-Requested-With, content-type, Authorization",
//     },
//   },
//   preview: {
//     port: 5173,
//     strictPort: true,
//     cors: true,
//     headers: {
//       "Access-Control-Allow-Origin": "*",
//     },
//   },
// });
