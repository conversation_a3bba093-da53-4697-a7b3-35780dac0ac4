{"name": "bot-ui", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "start": "vite preview --port 5173 --strictPort", "typecheck": "tsc"}, "dependencies": {"@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@reduxjs/toolkit": "^2.8.2", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router": "^7.5.3"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.4.1", "@react-router/dev": "^7.5.3", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "tailwindcss": "^4.1.4", "postcss": "^8.4.38", "autoprefixer": "^10.4.17", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}