import { importShared } from './__federation_fn_import-DTJInPHz.js';
import { j as jsxRuntimeExports } from './jsx-runtime-XI9uIe3W.js';
import { h as hydrateAuth, store } from './__federation_expose_Store-B-ZXwTSk.js';
import { u as useAppDispatch } from './redux-hooks-D0EmVyfl.js';

const {useEffect} = await importShared('react');
function AuthHydrator() {
  const dispatch = useAppDispatch();
  useEffect(() => {
    if (typeof window !== "undefined") {
      dispatch(hydrateAuth());
    }
  }, [dispatch]);
  return null;
}

const {isRouteErrorResponse,Links,Meta,Outlet,Scripts,ScrollRestoration} = await importShared('react-router');

const {Provider} = await importShared('react-redux');
const links = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous"
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
  }
];
function Layout({ children }) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("html", { lang: "en", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("head", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("meta", { charSet: "utf-8" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("meta", { name: "viewport", content: "width=device-width, initial-scale=1" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Meta, {}),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Links, {})
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("body", { children: [
      children,
      /* @__PURE__ */ jsxRuntimeExports.jsx(ScrollRestoration, {}),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Scripts, {})
    ] })
  ] });
}
function App() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Provider, { store, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(AuthHydrator, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})
  ] });
}
function ErrorBoundary({ error }) {
  let message = "Oops!";
  let details = "An unexpected error occurred.";
  let stack;
  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? "404" : "Error";
    details = error.status === 404 ? "The requested page could not be found." : error.statusText || details;
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("main", { className: "pt-16 p-4 container mx-auto", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { children: message }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: details }),
    stack
  ] });
}

export { ErrorBoundary, Layout, App as default, links };
