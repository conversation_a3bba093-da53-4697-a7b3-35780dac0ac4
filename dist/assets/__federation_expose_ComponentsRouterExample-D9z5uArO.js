import { importShared } from './__federation_fn_import-DTJInPHz.js';
import { j as jsxRuntimeExports } from './jsx-runtime-XI9uIe3W.js';
import routes from './__federation_expose_Routes-CoSTEVjW.js';

const {Link} = await importShared('react-router');
function RouterExample() {
  const agentId = "123";
  const department = "sales";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-2xl font-bold mb-4", children: "Router Example" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold mt-4 mb-2", children: "Static Routes" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "list-disc pl-6 mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.HOME, children: "Home" }),
        " - ",
        routes.HOME
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.AGENTS, children: "Agents" }),
        " - ",
        routes.AGENTS
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.NEW_AGENT, children: "New Agent" }),
        " - ",
        routes.NEW_AGENT
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold mt-4 mb-2", children: "Dynamic Routes with Parameters" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "list-disc pl-6 mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Link, { to: routes.AGENT_WITH_ID(agentId), children: [
          "Agent ",
          agentId
        ] }),
        " -",
        " ",
        routes.AGENT_WITH_ID(agentId)
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Link, { to: routes.AGENT_WITH_ID_AND_DEPARTMENT(agentId, department), children: [
          "Agent ",
          agentId,
          " in ",
          department
        ] }),
        " ",
        "- ",
        routes.AGENT_WITH_ID_AND_DEPARTMENT(agentId, department)
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.USER_PROFILE.invoke("456"), children: "User Profile (with invoke)" }),
        " ",
        "- ",
        routes.USER_PROFILE.invoke("456")
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.PRODUCT_DETAIL.invoke("electronics", "789"), children: "Product Detail (with invoke)" }),
        " ",
        "- ",
        routes.PRODUCT_DETAIL.invoke("electronics", "789")
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold mt-4 mb-2", children: "Dynamic Routes as Raw Patterns" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "list-disc pl-6 mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.USER_PROFILE, children: "User Profile (raw pattern)" }),
        " - ",
        routes.USER_PROFILE
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Link, { to: routes.PRODUCT_DETAIL, children: "Product Detail (raw pattern)" }),
        " - ",
        routes.PRODUCT_DETAIL
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold mt-4 mb-2", children: "Direct Access to Route Patterns" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "list-disc pl-6 mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "AGENT_WITH_ID direct access: ",
        routes.AGENT_WITH_ID
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "AGENT_WITH_ID_AND_DEPARTMENT direct access:",
        " ",
        routes.AGENT_WITH_ID_AND_DEPARTMENT
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "USER_PROFILE direct access: ",
        routes.USER_PROFILE
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "PRODUCT_DETAIL direct access: ",
        routes.PRODUCT_DETAIL
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold mt-4 mb-2", children: "Route Patterns via .pattern Property" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "list-disc pl-6 mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "AGENT_WITH_ID pattern: ",
        routes.AGENT_WITH_ID.pattern
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "AGENT_WITH_ID_AND_DEPARTMENT pattern:",
        " ",
        routes.AGENT_WITH_ID_AND_DEPARTMENT.pattern
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "USER_PROFILE pattern: ",
        routes.USER_PROFILE.pattern
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "PRODUCT_DETAIL pattern: ",
        routes.PRODUCT_DETAIL.pattern
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold mt-4 mb-2", children: "String Operations with Routes" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "list-disc pl-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "String concatenation: ",
        "Path: " + routes.USER_PROFILE
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { children: [
        "String interpolation: ",
        `Template: ${routes.PRODUCT_DETAIL}`
      ] })
    ] })
  ] });
}

export { RouterExample as default };
