import { importShared } from './__federation_fn_import-DTJInPHz.js';

const {createSlice} = await importShared('@reduxjs/toolkit');
const initialState = {
  chatbots: [
    {
      id: "1",
      name: "Customer Support Bot",
      description: "Automated customer support chatbot for handling common inquiries and support tickets.",
      status: "live",
      lastUpdated: "2 days ago",
      createdAt: "2024-01-15",
      flows: []
    },
    {
      id: "2",
      name: "Sales Assistant",
      description: "Lead qualification and sales support bot to help convert prospects into customers.",
      status: "draft",
      lastUpdated: "5 days ago",
      createdAt: "2024-01-10",
      flows: []
    },
    {
      id: "3",
      name: "Product Recommendation",
      description: "AI-powered product recommendation engine to help customers find the right products.",
      status: "live",
      lastUpdated: "2 days ago",
      createdAt: "2024-01-12",
      flows: []
    },
    {
      id: "4",
      name: "Onboarding Assistant",
      description: "Guide new users through the onboarding process with interactive tutorials.",
      status: "live",
      lastUpdated: "3 days ago",
      createdAt: "2024-01-08",
      flows: []
    },
    {
      id: "5",
      name: "FAQ Bot",
      description: "Frequently asked questions bot with intelligent search and instant answers.",
      status: "draft",
      lastUpdated: "1 day ago",
      createdAt: "2024-01-20",
      flows: []
    },
    {
      id: "6",
      name: "Booking Assistant",
      description: "Appointment scheduling and booking management chatbot for service businesses.",
      status: "live",
      lastUpdated: "4 days ago",
      createdAt: "2024-01-05",
      flows: []
    },
    {
      id: "7",
      name: "Feedback Collector",
      description: "Customer feedback and survey collection bot with sentiment analysis.",
      status: "draft",
      lastUpdated: "6 days ago",
      createdAt: "2024-01-03",
      flows: []
    },
    {
      id: "8",
      name: "Order Tracker",
      description: "Real-time order tracking and delivery status updates for e-commerce customers.",
      status: "live",
      lastUpdated: "1 week ago",
      createdAt: "2024-01-01",
      flows: []
    }
  ],
  selectedChatbot: null,
  loading: false,
  error: null,
  searchQuery: "",
  statusFilter: "all"
};
const chatbotsSlice = createSlice({
  name: "chatbots",
  initialState,
  reducers: {
    setChatbots: (state, action) => {
      state.chatbots = action.payload;
    },
    addChatbot: (state, action) => {
      state.chatbots.push(action.payload);
    },
    updateChatbot: (state, action) => {
      const index = state.chatbots.findIndex(
        (bot) => bot.id === action.payload.id
      );
      if (index !== -1) {
        state.chatbots[index] = action.payload;
      }
    },
    deleteChatbot: (state, action) => {
      state.chatbots = state.chatbots.filter(
        (bot) => bot.id !== action.payload
      );
    },
    setSelectedChatbot: (state, action) => {
      state.selectedChatbot = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    setStatusFilter: (state, action) => {
      state.statusFilter = action.payload;
    }
  }
});
const {
  setChatbots,
  addChatbot,
  updateChatbot,
  deleteChatbot,
  setSelectedChatbot,
  setLoading,
  setError,
  setSearchQuery,
  setStatusFilter
} = chatbotsSlice.actions;
const chatbotsReducer = chatbotsSlice.reducer;

export { setStatusFilter as a, chatbotsReducer as c, deleteChatbot as d, setSearchQuery as s };
