function createDynamicRoute(pattern) {
  const paramNames = pattern.match(/\[([^\]]+)\]/g)?.map((param) => param.substring(1, param.length - 1)) || [];
  const generateUrl = (params) => {
    if (params.length !== paramNames.length) {
      throw new Error(
        `Route "${pattern}" requires ${paramNames.length} parameters, but ${params.length} were provided.`
      );
    }
    let result = pattern;
    for (let i = 0; i < paramNames.length; i++) {
      result = result.replace(`[${paramNames[i]}]`, String(params[i]));
    }
    return result;
  };
  const routeFunction = function(...params) {
    if (params.length === 0 && paramNames.length > 0) {
      throw new Error(
        `Route "${pattern}" requires ${paramNames.length} parameters, but 0 were provided.`
      );
    }
    return generateUrl(params);
  };
  Object.defineProperty(routeFunction, "toString", {
    value: function() {
      return pattern;
    },
    writable: false,
    enumerable: false
  });
  Object.defineProperty(routeFunction, "pattern", {
    value: pattern,
    writable: false,
    enumerable: true
  });
  Object.defineProperty(routeFunction, "paramNames", {
    value: Object.freeze([...paramNames]),
    writable: false,
    enumerable: true
  });
  Object.defineProperty(routeFunction, "invoke", {
    value: function(...params) {
      if (params.length === 0) {
        if (paramNames.length === 0) {
          return pattern;
        }
        throw new Error(
          `Route "${pattern}" requires ${paramNames.length} parameters, but 0 were provided.`
        );
      }
      return generateUrl(params);
    },
    writable: false,
    enumerable: true
  });
  Object.defineProperty(routeFunction, "withParams", {
    value: function(params) {
      const extraParams = Object.keys(params).filter(
        (key) => !paramNames.includes(key)
      );
      if (extraParams.length > 0) {
        throw new Error(
          `Unknown parameter(s) for route "${pattern}": ${extraParams.join(
            ", "
          )}`
        );
      }
      const positionalParams = paramNames.map((name) => {
        if (!(name in params)) {
          throw new Error(`Missing parameter "${name}" for route "${pattern}"`);
        }
        return params[name];
      });
      return generateUrl(positionalParams);
    },
    writable: false,
    enumerable: true
  });
  Object.defineProperty(routeFunction, Symbol.toPrimitive, {
    value: function(_hint) {
      return pattern;
    },
    writable: false,
    enumerable: false
  });
  return routeFunction;
}
const routes = {
  // Static routes
  HOME: "/",
  AGENTS: "/agents",
  NEW_AGENT: "/agents/new",
  // Dynamic routes
  AGENT_WITH_ID: createDynamicRoute("/agents/[id]"),
  AGENT_WITH_ID_AND_DEPARTMENT: createDynamicRoute("/agents/[id]/[department]"),
  USER_PROFILE: createDynamicRoute("/users/[userId]/profile"),
  PRODUCT_DETAIL: createDynamicRoute("/products/[category]/[productId]")
};

export { routes as default };
